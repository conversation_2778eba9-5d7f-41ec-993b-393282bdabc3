package com.zenyte.game.model.ui.testinterfaces;

import com.near_reality.cache.interfaces.teleports.Category;
import com.near_reality.cache.interfaces.teleports.TeleportsList;
import com.zenyte.game.GameInterface;
import com.zenyte.game.model.ui.Interface;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.masks.UpdateFlag;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.entity.player.Skills;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.region.area.wilderness.WildernessArea;
import mgi.types.config.enums.Enums;

import java.util.HashMap;
import java.util.Map;

/**
 * Skills tab with proper mapping from component IDs to skill IDs and teleport functionality.
 */
public class SkillsTabInterface extends Interface {

    // Mapping from UI Component IDs to Skill IDs
    private static final Map<Integer, Integer> COMPONENT_ID_TO_SKILL = new HashMap<>();
    static {
        COMPONENT_ID_TO_SKILL.put(1, SkillConstants.ATTACK);
        COMPONENT_ID_TO_SKILL.put(2, SkillConstants.STRENGTH);
        COMPONENT_ID_TO_SKILL.put(3, SkillConstants.DEFENCE);
        COMPONENT_ID_TO_SKILL.put(4, SkillConstants.RANGED);
        COMPONENT_ID_TO_SKILL.put(5, SkillConstants.PRAYER);
        COMPONENT_ID_TO_SKILL.put(6, SkillConstants.MAGIC);
        COMPONENT_ID_TO_SKILL.put(7, SkillConstants.RUNECRAFTING);
        COMPONENT_ID_TO_SKILL.put(8, SkillConstants.CONSTRUCTION);
        COMPONENT_ID_TO_SKILL.put(9, SkillConstants.HITPOINTS);
        COMPONENT_ID_TO_SKILL.put(10, SkillConstants.AGILITY);
        COMPONENT_ID_TO_SKILL.put(11, SkillConstants.HERBLORE);
        COMPONENT_ID_TO_SKILL.put(12, SkillConstants.THIEVING);
        COMPONENT_ID_TO_SKILL.put(13, SkillConstants.CRAFTING);
        COMPONENT_ID_TO_SKILL.put(14, SkillConstants.FLETCHING);
        COMPONENT_ID_TO_SKILL.put(15, SkillConstants.SLAYER);
        COMPONENT_ID_TO_SKILL.put(16, SkillConstants.HUNTER);
        COMPONENT_ID_TO_SKILL.put(17, SkillConstants.MINING);
        COMPONENT_ID_TO_SKILL.put(18, SkillConstants.SMITHING);
        COMPONENT_ID_TO_SKILL.put(19, SkillConstants.FISHING);
        COMPONENT_ID_TO_SKILL.put(20, SkillConstants.COOKING);
        COMPONENT_ID_TO_SKILL.put(21, SkillConstants.FIREMAKING);
        COMPONENT_ID_TO_SKILL.put(22, SkillConstants.WOODCUTTING);
        COMPONENT_ID_TO_SKILL.put(23, SkillConstants.FARMING);
    }

    // Skill teleport data - contains skill names, IDs, and teleport coordinates
    private static final Map<Integer, SkillTeleportData> SKILL_TELEPORTS = new HashMap<>();
    static {
        // Combat Skills
        SKILL_TELEPORTS.put(SkillConstants.ATTACK, new SkillTeleportData("Attack", SkillConstants.ATTACK, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.STRENGTH, new SkillTeleportData("Strength", SkillConstants.STRENGTH, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.DEFENCE, new SkillTeleportData("Defence", SkillConstants.DEFENCE, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.RANGED, new SkillTeleportData("Ranged", SkillConstants.RANGED, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.PRAYER, new SkillTeleportData("Prayer", SkillConstants.PRAYER, new Location(3095, 3511, 0)));
        SKILL_TELEPORTS.put(SkillConstants.MAGIC, new SkillTeleportData("Magic", SkillConstants.MAGIC, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.HITPOINTS, new SkillTeleportData("Hitpoints", SkillConstants.HITPOINTS, new Location(1, 1, 1))); // Opens Skills Teleport Tab

        // Skilling Skills
        SKILL_TELEPORTS.put(SkillConstants.RUNECRAFTING, new SkillTeleportData("Runecrafting", SkillConstants.RUNECRAFTING, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.CONSTRUCTION, new SkillTeleportData("Construction", SkillConstants.CONSTRUCTION, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.AGILITY, new SkillTeleportData("Agility", SkillConstants.AGILITY, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.HERBLORE, new SkillTeleportData("Herblore", SkillConstants.HERBLORE, new Location(3083, 3511, 0)));
        SKILL_TELEPORTS.put(SkillConstants.THIEVING, new SkillTeleportData("Thieving", SkillConstants.THIEVING, new Location(3095, 3475, 0)));
        SKILL_TELEPORTS.put(SkillConstants.CRAFTING, new SkillTeleportData("Crafting", SkillConstants.CRAFTING, new Location(3107, 3498, 0)));
        SKILL_TELEPORTS.put(SkillConstants.FLETCHING, new SkillTeleportData("Fletching", SkillConstants.FLETCHING, new Location(1, 1, 1))); //No Teleports - Return Message
        SKILL_TELEPORTS.put(SkillConstants.SLAYER, new SkillTeleportData("Slayer", SkillConstants.SLAYER, new Location(3109, 3514, 0)));
        SKILL_TELEPORTS.put(SkillConstants.HUNTER, new SkillTeleportData("Hunter", SkillConstants.HUNTER, new Location(2525, 2915, 0)));
        SKILL_TELEPORTS.put(SkillConstants.MINING, new SkillTeleportData("Mining", SkillConstants.MINING, new Location(1, 1, 1))); // Opens Skills Teleport Tab
        SKILL_TELEPORTS.put(SkillConstants.SMITHING, new SkillTeleportData("Smithing", SkillConstants.SMITHING, new Location(3108, 3499, 0)));
        SKILL_TELEPORTS.put(SkillConstants.FISHING, new SkillTeleportData("Fishing", SkillConstants.FISHING, new Location(3107, 3498, 0)));
        SKILL_TELEPORTS.put(SkillConstants.COOKING, new SkillTeleportData("Cooking", SkillConstants.COOKING, new Location(3109, 3498, 0)));
        SKILL_TELEPORTS.put(SkillConstants.FIREMAKING, new SkillTeleportData("Firemaking", SkillConstants.FIREMAKING, new Location(1630, 3949, 0)));
        SKILL_TELEPORTS.put(SkillConstants.WOODCUTTING, new SkillTeleportData("Woodcutting", SkillConstants.WOODCUTTING, new Location(1602, 3498, 0)));
        SKILL_TELEPORTS.put(SkillConstants.FARMING, new SkillTeleportData("Farming", SkillConstants.FARMING, new Location(1, 1, 1))); // Opens Skills Teleport Tab
    }

    /**
     * Inner class to hold skill teleport data
     */
    private static class SkillTeleportData {
        private final String skillName;
        private final int skillId;
        private final Location teleportLocation;

        public SkillTeleportData(String skillName, int skillId, Location teleportLocation) {
            this.skillName = skillName;
            this.skillId = skillId;
            this.teleportLocation = teleportLocation;
        }

        public String getSkillName() {
            return skillName;
        }

        public int getSkillId() {
            return skillId;
        }

        public Location getTeleportLocation() {
            return teleportLocation;
        }

        public boolean hasValidCoordinates() {
            return teleportLocation.getX() != 1 && teleportLocation.getY() != 1;
        }

        public void teleport(Player player) {

            /*if (!hasValidCoordinates()) {
                player.sendMessage("Teleport is not setup for this slayer monster");
                return;
            }*/

            // TODO: Add teleport animation and effects
            player.setLocation(teleportLocation);
            player.sendMessage("You have been teleported to the " + skillName + " training area.");
        }
    }

    @Override
    protected DefaultClickHandler getDefaultHandler() {
        return (player, componentId, slotId, itemId, optionId) -> {
            if (optionId == 1) { // Mobile XP popup
                return;
            }
            if (player.isLocked()) {
                return;
            }
            if (player.isUnderCombat()) {
                player.sendMessage("You can't do this while in combat.");
                return;
            }

            Integer skill = COMPONENT_ID_TO_SKILL.get(componentId);
            if (skill == SkillConstants.CONSTRUCTION) {
                player.sendMessage("Construction has not been finished yet.");
                return;
            }
            if (skill == null) {
                return;
            }

            if (player.isDebugging) {
                player.sendMessage("componentId: " + componentId + " - componentIdToSkill: "+COMPONENT_ID_TO_SKILL.get(componentId)+" - skillId: " + skill);
            }
            player.getDialogueManager().start(new Dialogue(player) {
                @Override
                public void buildDialogue() {
                    options("Select an option for " + Skills.getSkillName(skill),
                            new DialogueOption("View Skill Guide", () -> player.getSkills().sendSkillMenu(Enums.SKILL_GUIDES_ENUM.getKey(SkillConstants.SKILLS[skill]).orElseThrow(RuntimeException::new), 0)),
                            new DialogueOption("Teleport", () -> {
                                SkillTeleportData skillTeleport = SKILL_TELEPORTS.get(skill);
                                if (player.isDebugging) {
                                    player.sendMessage("skill: " + skill);
                                }
                                if (skill == SkillConstants.FLETCHING) {
                                    player.sendMessage("There is no fletching location. You can fletch anywhere.");
                                    return;
                                }
                                if (skillTeleport != null) {
                                    if (skillTeleport.hasValidCoordinates()) {
                                        skillTeleport.teleport(player);
                                    } else {
                                        if (skill == SkillConstants.ATTACK || skill == SkillConstants.STRENGTH || skill == SkillConstants.DEFENCE || skill == SkillConstants.RANGED || skill == SkillConstants.MAGIC || skill == SkillConstants.HITPOINTS)
                                            handleSkillsTeleport(player, 0);
                                        else
                                            handleSkillsTeleport(player, 1);
                                    }
                                } else {
                                    player.sendMessage("Teleport is not available for this skill.");
                                }
                            })
                    );
                }
            });
        };
    }

    @Override
    protected void attach() {

    }

    @Override
    public void open(final Player player) {
        player.getInterfaceHandler().sendInterface(this);
    }

    @Override
    protected void build() {

    }

    @Override
    public GameInterface getInterface() {
        return GameInterface.SKILLS_TAB;
    }

    /**
     * Handles special teleport behavior for Agility (opens Skilling teleports category)
     * Opens the teleport interface and automatically selects the Skilling teleports category
     * @param player the player
     */
    private static void handleSkillsTeleport(Player player, int skillType) {
        String categoryName = null;
        if (skillType == 0)
            categoryName = "training teleports";
        else
            categoryName = "skilling teleports";
        final Category category = TeleportsList.getCategories().get(categoryName);
        if (category != null) {
            player.getVarManager().sendVar(261, category.getId());
            player.getTeleportsManager().setSelectedCategory(category);
            player.getTeleportsManager().attemptOpen();
            player.sendMessage("cat getID : "+category.getId()+" category:"+category+" categoryName:"+categoryName);
        }
    }
}
